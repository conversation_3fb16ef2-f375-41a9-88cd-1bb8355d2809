"use client";

import React, { useState } from "react";
import CasesTable from "./CasesTable";

interface DashboardBoxProps {
  title: string;
  value: number | string;
  description?: string;
  bgColor?: string;
}

const DashboardBox: React.FC<DashboardBoxProps> = ({
  title,
  value,
  description,
  bgColor = "bg-white",
}) => (
  <div
    className={`flex items-center space-x-4 p-6 rounded-lg shadow-md ${bgColor}`}
  >
    <div>
      <h3 className="text-gray-500 font-semibold">{title}</h3>
      <p className="text-3xl font-bold">{value}</p>
      {description && (
        <p className="text-gray-400 text-sm mt-1">{description}</p>
      )}
    </div>
  </div>
);

const sampleCases = [
  {
    id: "C001",
    caseType: "Dependent Visas",
    userName: "<PERSON>",
    caseStatus: "Open",
    priority: "High",
    startDate: "2025-05-01",
    endDate: "2025-05-15",
  },
  {
    id: "C002",
    caseType: "Stamp Extensions",
    userName: "<PERSON>",
    caseStatus: "Closed",
    priority: "Medium",
    startDate: "2025-04-10",
    endDate: "2025-05-10",
  },
  {
    id: "C003",
    caseType: "Work Permit Applications",
    userName: "Muklesh",
    caseStatus: "Pending",
    priority: "Low",
    startDate: "2025-04-10",
    endDate: "2025-05-10",
  },
  // ... more cases
];

const ITEMS_PER_PAGE = 5;

const ImmigrationDashboard: React.FC = () => {
  const openCases = 27;
  const closedCases = 58;
  const pendingCases = 12;
  const totalCases = openCases + closedCases + pendingCases;

  const [currentPage, setCurrentPage] = useState(1);

  return (
    <div className="p-4 bg-gray-50 rounded-md min-h-[400px]">
      <h2 className="text-2xl font-bold mb-6">Immigration Dashboard</h2>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 mb-8">
        <DashboardBox
          title="Open Cases"
          value={openCases}
          description="Currently active cases"
          bgColor="bg-blue-100"
        />
        <DashboardBox
          title="Closed Cases"
          value={closedCases}
          description="Successfully resolved"
          bgColor="bg-green-100"
        />
        <DashboardBox
          title="Pending Cases"
          value={pendingCases}
          description="Awaiting updates"
          bgColor="bg-yellow-100"
        />
        <DashboardBox
          title="Total Cases"
          value={totalCases}
          description="All cases combined"
          bgColor="bg-purple-100"
        />
      </div>

      {/* Pass pagination props and handlers to CasesTable */}
      <CasesTable
        cases={sampleCases}
        currentPage={currentPage}
        itemsPerPage={ITEMS_PER_PAGE}
        onPageChange={setCurrentPage}
      />
    </div>
  );
};

export default ImmigrationDashboard;
